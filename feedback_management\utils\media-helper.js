/**
 * 多媒体处理工具类
 * 统一处理图片预览、视频播放、音频播放等功能
 */
class MediaHelper {
  
  /**
   * 初始化多媒体助手
   * @param {Object} pageContext - 页面上下文
   */
  constructor(pageContext) {
    this.page = pageContext;
    this.audioContext = null;
    this.currentPlayingIndex = -1;
    this.mediaStates = {
      imageLoadErrors: {},
      videoLoadErrors: {},
      audioLoadErrors: {},
      playingProgress: {},
    };
  }

  /**
   * 处理媒体文件URL路径
   * @param {Object} mediaItem - 媒体文件对象
   * @returns {string} 处理后的完整URL
   */
  processMediaUrl(mediaItem) {
    if (!mediaItem || !mediaItem.file_path) {
      console.warn('媒体文件路径为空:', mediaItem);
      return '';
    }

    const app = getApp();
    let fullPath = '';

    // 如果已经是完整的HTTP URL，直接使用
    if (mediaItem.file_path.startsWith("http://") || mediaItem.file_path.startsWith("https://")) {
      fullPath = mediaItem.file_path;
    }
    // 如果是以/api/files/开头的路径，拼接baseUrl
    else if (mediaItem.file_path.startsWith("/api/files/")) {
      fullPath = `${app.globalData.baseUrl || app.globalData.feedbackBaseUrl}${mediaItem.file_path}`;
    }
    // 如果是以/upload/开头的路径，直接拼接baseUrl
    else if (mediaItem.file_path.startsWith("/upload/")) {
      fullPath = `${app.globalData.baseUrl || app.globalData.feedbackBaseUrl}${mediaItem.file_path}`;
    }
    // 如果是相对路径，尝试/api/files/前缀
    else {
      fullPath = `${app.globalData.baseUrl}/api/files/${mediaItem.file_path}`;
    }

    return fullPath;
  }

  /**
   * 预览图片（支持高清原图）
   * @param {string} currentSrc - 当前图片URL
   * @param {Array} imageList - 图片列表
   * @param {Object} options - 预览选项
   */
  previewImages(currentSrc, imageList = [], options = {}) {
    const {
      enableOriginal = true,
      showMenuItems = ['saveImage', 'shareImage'],
      enableRotate = true,
      enableZoom = true
    } = options;

    // 构建图片URL列表
    const urls = imageList.length > 0 
      ? imageList.map(item => typeof item === 'string' ? item : item.file_path)
      : [currentSrc];

    // 过滤掉无效的URL
    const validUrls = urls.filter(url => url && url.trim() !== '');

    if (validUrls.length === 0) {
      wx.showToast({
        title: '没有可预览的图片',
        icon: 'none'
      });
      return;
    }

    wx.previewImage({
      current: currentSrc,
      urls: validUrls,
      showmenu: showMenuItems.length > 0,
      success: (res) => {
        console.log('图片预览成功:', res);
      },
      fail: (error) => {
        console.error('图片预览失败:', error);
        this.handleImagePreviewError(error, currentSrc);
      }
    });
  }

  /**
   * 处理图片预览错误
   * @param {Object} error - 错误对象
   * @param {string} src - 图片URL
   */
  handleImagePreviewError(error, src) {
    let message = '图片预览失败';
    
    if (error.errMsg) {
      if (error.errMsg.includes('network')) {
        message = '网络连接异常，请检查网络后重试';
      } else if (error.errMsg.includes('url')) {
        message = '图片地址无法访问';
      }
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });

    // 尝试备用URL
    this.tryAlternativeImageUrl(src);
  }

  /**
   * 尝试备用图片URL
   * @param {string} originalSrc - 原始图片URL
   */
  tryAlternativeImageUrl(originalSrc) {
    if (originalSrc && originalSrc.includes('/api/files/')) {
      const backupSrc = originalSrc.replace('/api/files/', '/upload/');
      console.log('尝试备用图片路径:', backupSrc);
      
      // 测试备用URL是否可用
      wx.getImageInfo({
        src: backupSrc,
        success: (res) => {
          console.log('备用图片路径可用:', res);
          // 可以在这里更新页面数据中的图片URL
        },
        fail: (error) => {
          console.error('备用图片路径也不可用:', error);
        }
      });
    }
  }

  /**
   * 处理图片加载错误
   * @param {Object} event - 错误事件
   * @param {Object} options - 处理选项
   */
  handleImageLoadError(event, options = {}) {
    const { src, index, retryCallback } = options;
    
    console.error('图片加载错误:', {
      src: src,
      index: index,
      event: event
    });

    // 标记图片加载错误
    if (index !== undefined) {
      this.mediaStates.imageLoadErrors[index] = true;
    }

    // 尝试备用路径
    if (src && src.includes('/api/files/')) {
      const alternativeUrl = src.replace('/api/files/', '/upload/');
      
      // 测试备用URL
      wx.getImageInfo({
        src: alternativeUrl,
        success: (res) => {
          if (retryCallback && typeof retryCallback === 'function') {
            retryCallback(alternativeUrl, index);
          }
        },
        fail: (error) => {
          console.error('备用图片路径也失败:', error);
          wx.showToast({
            title: '图片加载失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    } else {
      wx.showToast({
        title: '图片加载失败',
        icon: 'none',
        duration: 2000
      });
    }
  }

  /**
   * 播放音频
   * @param {number} index - 音频索引
   * @param {string} src - 音频URL
   * @param {Object} options - 播放选项
   */
  playAudio(index, src, options = {}) {
    const {
      onPlay = () => {},
      onPause = () => {},
      onEnded = () => {},
      onError = () => {},
      onTimeUpdate = () => {}
    } = options;

    // 停止当前播放的音频
    this.stopAllAudio();

    // 创建新的音频上下文
    this.audioContext = wx.createInnerAudioContext();
    this.audioContext.src = src;
    this.currentPlayingIndex = index;

    // 监听播放事件
    this.audioContext.onPlay(() => {
      console.log('音频开始播放:', index);
      onPlay(index);
    });

    this.audioContext.onPause(() => {
      console.log('音频暂停:', index);
      this.currentPlayingIndex = -1;
      onPause(index);
    });

    this.audioContext.onEnded(() => {
      console.log('音频播放结束:', index);
      this.currentPlayingIndex = -1;
      this.audioContext = null;
      onEnded(index);
    });

    this.audioContext.onError((error) => {
      console.error('音频播放错误:', error);
      this.currentPlayingIndex = -1;
      this.audioContext = null;
      onError(error, index);
      
      wx.showToast({
        title: '音频播放失败',
        icon: 'none'
      });
    });

    // 监听播放进度
    this.audioContext.onTimeUpdate(() => {
      const currentTime = this.audioContext.currentTime;
      const duration = this.audioContext.duration;
      onTimeUpdate(currentTime, duration, index);
    });

    // 开始播放
    this.audioContext.play();
  }

  /**
   * 暂停音频
   * @param {number} index - 音频索引
   */
  pauseAudio(index) {
    if (this.audioContext && this.currentPlayingIndex === index) {
      this.audioContext.pause();
    }
  }

  /**
   * 停止所有音频
   */
  stopAllAudio() {
    if (this.audioContext) {
      this.audioContext.stop();
      this.audioContext.destroy();
      this.audioContext = null;
    }
    this.currentPlayingIndex = -1;
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 B";
    if (!bytes || typeof bytes !== 'number') return '';
    
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * 格式化时长
   * @param {number} seconds - 秒数
   * @returns {string} 格式化后的时长
   */
  formatDuration(seconds) {
    if (!seconds || seconds <= 0) return "未知";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }

  /**
   * 下载媒体文件
   * @param {Object} mediaItem - 媒体文件对象
   * @param {Object} options - 下载选项
   */
  downloadMedia(mediaItem, options = {}) {
    const { showProgress = true } = options;

    if (!mediaItem || !mediaItem.file_path) {
      wx.showToast({
        title: '文件路径无效',
        icon: 'none'
      });
      return;
    }

    if (showProgress) {
      wx.showLoading({
        title: '下载中...',
        mask: true
      });
    }

    // 根据文件类型选择不同的下载方式
    if (mediaItem.file_type === 'image') {
      this.downloadImage(mediaItem.file_path, mediaItem.file_name);
    } else {
      this.downloadFile(mediaItem.file_path, mediaItem.file_name);
    }
  }

  /**
   * 下载图片到相册
   * @param {string} url - 图片URL
   * @param {string} fileName - 文件名
   */
  downloadImage(url, fileName) {
    wx.downloadFile({
      url: url,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading();
              wx.showToast({
                title: '已保存到相册',
                icon: 'success'
              });
            },
            fail: (error) => {
              wx.hideLoading();
              if (error.errMsg.includes('auth')) {
                wx.showModal({
                  title: '提示',
                  content: '需要授权访问相册才能保存图片',
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting();
                    }
                  }
                });
              } else {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              }
            }
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('图片下载失败:', error);
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    });
  }

  /**
   * 下载文件
   * @param {string} url - 文件URL
   * @param {string} fileName - 文件名
   */
  downloadFile(url, fileName) {
    wx.downloadFile({
      url: url,
      success: (res) => {
        wx.hideLoading();
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: () => {
              console.log('文件打开成功');
            },
            fail: (error) => {
              console.error('文件打开失败:', error);
              wx.showToast({
                title: '文件打开失败',
                icon: 'none'
              });
            }
          });
        } else {
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('文件下载失败:', error);
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    });
  }

  /**
   * 分享媒体文件
   * @param {Object} mediaItem - 媒体文件对象
   * @param {Object} options - 分享选项
   */
  shareMedia(mediaItem, options = {}) {
    const { title = '分享文件' } = options;

    if (!mediaItem || !mediaItem.file_path) {
      wx.showToast({
        title: '文件路径无效',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.shareToFriend(mediaItem, title);
            break;
          case 1:
            this.shareToMoments(mediaItem, title);
            break;
          case 2:
            this.copyLink(mediaItem.file_path);
            break;
        }
      }
    });
  }

  /**
   * 分享给朋友
   * @param {Object} mediaItem - 媒体文件对象
   * @param {string} title - 分享标题
   */
  shareToFriend(mediaItem, title) {
    // 这里可以实现分享给朋友的逻辑
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  }

  /**
   * 分享到朋友圈
   * @param {Object} mediaItem - 媒体文件对象
   * @param {string} title - 分享标题
   */
  shareToMoments(mediaItem, title) {
    // 这里可以实现分享到朋友圈的逻辑
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    });
  }

  /**
   * 复制链接
   * @param {string} url - 文件URL
   */
  copyLink(url) {
    wx.setClipboardData({
      data: url,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  }

  /**
   * 销毁多媒体助手
   */
  destroy() {
    this.stopAllAudio();
    this.mediaStates = {
      imageLoadErrors: {},
      videoLoadErrors: {},
      audioLoadErrors: {},
      playingProgress: {},
    };
  }
}

module.exports = MediaHelper;
