[2025-08-01 00:32:06] [DEBUG] 认证检查 | {"hasToken":false,"tokenLength":0}
[2025-08-01 00:32:06] [DEBUG] 认证检查 | {"hasToken":false,"tokenLength":0}
[2025-08-01 00:32:06] [DEBUG] 认证检查 | {"hasToken":false,"tokenLength":0}
[2025-08-01 00:35:13] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":36}
[2025-08-01 00:35:13] [DEBUG] Token解码结果 | {"decoded":"2023043��}�:1754008513283\u0000\u0000"}
[2025-08-01 00:35:13] [DEBUG] Token解析结果 | {"userId":"2023043��}�","username":"1754008513283\u0000\u0000","userIdType":"string"}
[2025-08-01 00:35:13] [DEBUG] 用户ID解析 | {"originalUserId":"2023043��}�","userIdType":"string"}
[2025-08-01 00:35:13] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:35:13] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:35:13] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":36}
[2025-08-01 00:35:13] [DEBUG] Token解码结果 | {"decoded":"2023043��}�:1754008513295\u0000\u0000"}
[2025-08-01 00:35:13] [DEBUG] Token解析结果 | {"userId":"2023043��}�","username":"1754008513295\u0000\u0000","userIdType":"string"}
[2025-08-01 00:35:13] [DEBUG] 用户ID解析 | {"originalUserId":"2023043��}�","userIdType":"string"}
[2025-08-01 00:35:13] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:35:13] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:35:13] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:35:13] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:35:14] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:35:14] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:35:14] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:35:14] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:35:14] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:35:14] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:35:14] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:35:14] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":36}
[2025-08-01 00:35:14] [DEBUG] Token解码结果 | {"decoded":"2023043��}�:1754008514858\u0000\u0000"}
[2025-08-01 00:35:14] [DEBUG] Token解析结果 | {"userId":"2023043��}�","username":"1754008514858\u0000\u0000","userIdType":"string"}
[2025-08-01 00:35:14] [DEBUG] 用户ID解析 | {"originalUserId":"2023043��}�","userIdType":"string"}
[2025-08-01 00:35:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:35:14] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:35:14] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:35:14] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:35:14] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:35:14] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:35:14] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:35:14] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:35:14] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:35:15] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:03] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":36}
[2025-08-01 00:36:03] [DEBUG] Token解码结果 | {"decoded":"2023043��}�:1754008563766\u0000\u0000"}
[2025-08-01 00:36:03] [DEBUG] Token解析结果 | {"userId":"2023043��}�","username":"1754008563766\u0000\u0000","userIdType":"string"}
[2025-08-01 00:36:03] [DEBUG] 用户ID解析 | {"originalUserId":"2023043��}�","userIdType":"string"}
[2025-08-01 00:36:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:36:03] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:36:03] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":36}
[2025-08-01 00:36:03] [DEBUG] Token解码结果 | {"decoded":"2023043��}�:1754008563780\u0000\u0000"}
[2025-08-01 00:36:03] [DEBUG] Token解析结果 | {"userId":"2023043��}�","username":"1754008563780\u0000\u0000","userIdType":"string"}
[2025-08-01 00:36:03] [DEBUG] 用户ID解析 | {"originalUserId":"2023043��}�","userIdType":"string"}
[2025-08-01 00:36:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:36:03] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:36:03] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:36:03] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:36:03] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:03] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:36:03] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:36:03] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:03] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:36:03] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:36:03] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:04] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":36}
[2025-08-01 00:36:04] [DEBUG] Token解码结果 | {"decoded":"2023043��}�:1754008564001\u0000\u0000"}
[2025-08-01 00:36:04] [DEBUG] Token解析结果 | {"userId":"2023043��}�","username":"1754008564001\u0000\u0000","userIdType":"string"}
[2025-08-01 00:36:04] [DEBUG] 用户ID解析 | {"originalUserId":"2023043��}�","userIdType":"string"}
[2025-08-01 00:36:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:36:04] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:36:04] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:36:04] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:36:04] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:36:04] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:04] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:36:04] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:36:04] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:36:04] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:30] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":36}
[2025-08-01 00:36:30] [DEBUG] Token解码结果 | {"decoded":"2023043��}�:1754008590556\u0000\u0000"}
[2025-08-01 00:36:30] [DEBUG] Token解析结果 | {"userId":"2023043��}�","username":"1754008590556\u0000\u0000","userIdType":"string"}
[2025-08-01 00:36:30] [DEBUG] 用户ID解析 | {"originalUserId":"2023043��}�","userIdType":"string"}
[2025-08-01 00:36:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:36:30] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:36:30] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":36}
[2025-08-01 00:36:30] [DEBUG] Token解码结果 | {"decoded":"2023043��}�:1754008590575\u0000\u0000"}
[2025-08-01 00:36:30] [DEBUG] Token解析结果 | {"userId":"2023043��}�","username":"1754008590575\u0000\u0000","userIdType":"string"}
[2025-08-01 00:36:30] [DEBUG] 用户ID解析 | {"originalUserId":"2023043��}�","userIdType":"string"}
[2025-08-01 00:36:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:36:30] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:36:30] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:36:30] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:36:30] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:30] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:36:30] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:36:30] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:30] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:36:30] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:36:30] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:30] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":36}
[2025-08-01 00:36:30] [DEBUG] Token解码结果 | {"decoded":"2023043��}�:1754008590875\u0000\u0000"}
[2025-08-01 00:36:30] [DEBUG] Token解析结果 | {"userId":"2023043��}�","username":"1754008590875\u0000\u0000","userIdType":"string"}
[2025-08-01 00:36:30] [DEBUG] 用户ID解析 | {"originalUserId":"2023043��}�","userIdType":"string"}
[2025-08-01 00:36:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:36:30] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:36:30] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:36:30] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:36:30] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:36:30] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:36:30] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:36:30] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:36:30] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:36:30] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:37:55] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:37:55] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008675968\u0000\u0000"}
[2025-08-01 00:37:55] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:37:55] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:37:55] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:37:55] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:37:55] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:37:55] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008675982\u0000\u0000"}
[2025-08-01 00:37:55] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:37:56] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:37:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:37:56] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:37:56] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:37:56] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:37:56] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:37:56] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:37:56] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:37:56] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:37:56] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:37:56] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:37:56] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:37:56] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:37:56] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008676190\u0000\u0000"}
[2025-08-01 00:37:56] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:37:56] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:37:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:37:56] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:37:56] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:37:56] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:37:56] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:37:56] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:37:56] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:37:56] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:37:56] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:37:56] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:17] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:38:17] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008697059\u0000\u0000"}
[2025-08-01 00:38:17] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:38:17] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:38:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:38:17] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:38:17] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:38:17] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008697073\u0000\u0000"}
[2025-08-01 00:38:17] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:38:17] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:38:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:38:17] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:38:17] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:38:17] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:38:17] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:17] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:38:17] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:38:17] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:17] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:38:17] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:38:17] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:17] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:38:17] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008697286\u0000\u0000"}
[2025-08-01 00:38:17] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:38:17] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:38:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:38:17] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:38:17] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:38:17] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:38:17] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:38:17] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:17] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:38:17] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:38:17] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:38:17] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:43] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:38:43] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008723131\u0000\u0000"}
[2025-08-01 00:38:43] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:38:43] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:38:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:38:43] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:38:43] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:38:43] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008723146\u0000\u0000"}
[2025-08-01 00:38:43] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:38:43] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:38:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:38:43] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:38:43] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:38:43] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:38:43] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:43] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:38:43] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:38:43] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:43] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:38:43] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:38:43] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:43] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:38:43] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008723356\u0000\u0000"}
[2025-08-01 00:38:43] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:38:43] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:38:43] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:38:43] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:38:43] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:38:43] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:38:43] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:38:43] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:38:43] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:38:43] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:38:43] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:38:43] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":8}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:08] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:08] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008748589\u0000\u0000"}
[2025-08-01 00:39:08] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:08] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:08] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:08] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:08] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008748633\u0000\u0000"}
[2025-08-01 00:39:08] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:08] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:08] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:08] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = ? AND cp.X_OrgId = ?\n      ","params":["B05-2200252","1007"]}
[2025-08-01 00:39:08] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:39:08] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:08] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)","params":["B05-2200252","2023043","2025-08-01 08:38:00","","反馈类别5",118.14577,24.5118,"福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城 (禹洲·香槟城北门) (24.511800, 118.145770)","authorized"]}
[2025-08-01 00:39:08] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (@param0, @param1, @param2, @param3, @param4, @param5, @param6, @param7, @param8); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:39:09] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5010}]],"recordset":[{"insertId":5010}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:39:09] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (?, ?, ?, ?, ?)","params":[5010,"image","file-1754008748619-328054685.jpg","/api/files/images/file-1754008748619-328054685.jpg",180373]}
[2025-08-01 00:39:09] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (@param0, @param1, @param2, @param3, @param4); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:39:09] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5012}]],"recordset":[{"insertId":5012}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:39:11] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:11] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008751879\u0000\u0000"}
[2025-08-01 00:39:11] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:11] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:11] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:11] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:11] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:11] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008751894\u0000\u0000"}
[2025-08-01 00:39:11] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:11] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:11] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:11] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:11] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:39:11] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:39:11] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:11] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:39:11] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:39:11] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:11] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:39:11] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:39:11] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:12] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:12] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008752093\u0000\u0000"}
[2025-08-01 00:39:12] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:12] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:12] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:12] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:12] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:39:12] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:39:12] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:39:12] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:12] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:39:12] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:39:12] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:39:12] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":9}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":9}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:24] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:24] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008764756\u0000\u0000"}
[2025-08-01 00:39:24] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:24] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:24] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:24] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:24] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008764783\u0000\u0000"}
[2025-08-01 00:39:24] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:24] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:24] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:24] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = ? AND cp.X_OrgId = ?\n      ","params":["B05-2200252","1007"]}
[2025-08-01 00:39:24] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:39:24] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:24] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)","params":["B05-2200252","2023043","2025-08-01 08:39:00","","反馈类别4",118.14577,24.5118,"福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城 (禹洲·香槟城北门) (24.511800, 118.145770)","authorized"]}
[2025-08-01 00:39:24] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (@param0, @param1, @param2, @param3, @param4, @param5, @param6, @param7, @param8); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:39:24] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5011}]],"recordset":[{"insertId":5011}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:39:24] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (?, ?, ?, ?, ?)","params":[5011,"image","file-1754008764773-580988431.jpg","/api/files/images/file-1754008764773-580988431.jpg",181082]}
[2025-08-01 00:39:24] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (@param0, @param1, @param2, @param3, @param4); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:39:24] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5013}]],"recordset":[{"insertId":5013}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:39:28] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:28] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008768017\u0000\u0000"}
[2025-08-01 00:39:28] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:28] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:28] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:28] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:28] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008768034\u0000\u0000"}
[2025-08-01 00:39:28] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:28] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:28] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:28] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:39:28] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:39:28] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:28] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:39:28] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:39:28] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:28] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:39:28] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:39:28] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:28] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:39:28] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008768295\u0000\u0000"}
[2025-08-01 00:39:28] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:39:28] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:39:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:39:28] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:39:28] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:39:28] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:39:28] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:39:28] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:39:28] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:39:28] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:39:28] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:39:28] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":10}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":10}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:02] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:02] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008802044\u0000\u0000"}
[2025-08-01 00:40:02] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:02] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:02] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:02] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:02] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008802102\u0000\u0000"}
[2025-08-01 00:40:02] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:02] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:02] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:02] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:02] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008802181\u0000\u0000"}
[2025-08-01 00:40:02] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:02] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:02] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:02] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:02] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008802218\u0000\u0000"}
[2025-08-01 00:40:02] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:02] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:02] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:02] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = ? AND cp.X_OrgId = ?\n      ","params":["B05-2200252","1007"]}
[2025-08-01 00:40:02] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:40:02] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:02] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)","params":["B05-2200252","2023043","2025-08-01 08:39:00","","反馈类别6",118.14577,24.5118,"福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城 (禹洲·香槟城北门) (24.511800, 118.145770)","authorized"]}
[2025-08-01 00:40:02] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (@param0, @param1, @param2, @param3, @param4, @param5, @param6, @param7, @param8); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:40:02] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5012}]],"recordset":[{"insertId":5012}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:40:02] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (?, ?, ?, ?, ?)","params":[5012,"image","file-1754008802065-152236924.jpg","/api/files/images/file-1754008802065-152236924.jpg",180684]}
[2025-08-01 00:40:02] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (@param0, @param1, @param2, @param3, @param4); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:40:02] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5014}]],"recordset":[{"insertId":5014}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:40:02] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (?, ?, ?, ?, ?)","params":[5012,"video","file-1754008802120-544892457.webm","/api/files/videos/file-1754008802120-544892457.webm",1953933]}
[2025-08-01 00:40:02] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (@param0, @param1, @param2, @param3, @param4); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:40:02] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5015}]],"recordset":[{"insertId":5015}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:40:02] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (?, ?, ?, ?, ?)","params":[5012,"audio","file-1754008802209-482866387.mp3","/api/files/records/file-1754008802209-482866387.mp3",27362]}
[2025-08-01 00:40:02] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (@param0, @param1, @param2, @param3, @param4); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:40:02] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5016}]],"recordset":[{"insertId":5016}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:40:05] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:05] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008805504\u0000\u0000"}
[2025-08-01 00:40:05] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:05] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:05] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:05] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:05] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008805517\u0000\u0000"}
[2025-08-01 00:40:05] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:05] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:05] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:05] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:40:05] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:40:05] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:05] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:40:05] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:40:05] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:05] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:40:05] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:40:05] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:05] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:05] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008805695\u0000\u0000"}
[2025-08-01 00:40:05] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:05] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:05] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:05] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:40:05] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:40:05] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:40:05] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:05] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:40:05] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:40:05] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:40:05] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":11}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":11}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:29] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:29] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008829029\u0000\u0000"}
[2025-08-01 00:40:29] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:29] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:29] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:29] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:29] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008829079\u0000\u0000"}
[2025-08-01 00:40:29] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:29] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:29] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:29] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:29] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008829118\u0000\u0000"}
[2025-08-01 00:40:29] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:29] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:29] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:29] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:29] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008829152\u0000\u0000"}
[2025-08-01 00:40:29] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:29] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:29] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:29] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:29] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = ? AND cp.X_OrgId = ?\n      ","params":["B05-2200252","1007"]}
[2025-08-01 00:40:29] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:40:29] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:29] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)","params":["B05-2200252","2023043","2025-08-01 08:40:00","","反馈类别6",118.14577,24.5118,"福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城 (禹洲·香槟城北门) (24.511800, 118.145770)","authorized"]}
[2025-08-01 00:40:29] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (@param0, @param1, @param2, @param3, @param4, @param5, @param6, @param7, @param8); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:40:29] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5013}]],"recordset":[{"insertId":5013}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:40:29] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (?, ?, ?, ?, ?)","params":[5013,"image","file-1754008829050-74657785.jpg","/api/files/images/file-1754008829050-74657785.jpg",182167]}
[2025-08-01 00:40:29] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (@param0, @param1, @param2, @param3, @param4); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:40:29] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5017}]],"recordset":[{"insertId":5017}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:40:29] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (?, ?, ?, ?, ?)","params":[5013,"video","file-1754008829096-604672902.webm","/api/files/videos/file-1754008829096-604672902.webm",1879910]}
[2025-08-01 00:40:29] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (@param0, @param1, @param2, @param3, @param4); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:40:29] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5018}]],"recordset":[{"insertId":5018}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:40:29] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (?, ?, ?, ?, ?)","params":[5013,"audio","file-1754008829145-563302887.mp3","/api/files/records/file-1754008829145-563302887.mp3",8127]}
[2025-08-01 00:40:29] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO CU_feedback_media\n       (FeedbackId, MediaType, FileName, FilePath, FileSize)\n       VALUES (@param0, @param1, @param2, @param3, @param4); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:40:29] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5019}]],"recordset":[{"insertId":5019}],"output":{},"rowsAffected":[1,1]}}
[2025-08-01 00:40:32] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:32] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008832386\u0000\u0000"}
[2025-08-01 00:40:32] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:32] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:32] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:32] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:32] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008832399\u0000\u0000"}
[2025-08-01 00:40:32] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:32] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:32] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:32] [DEBUG] 执行SQL查询 | {"query":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = ?","params":["B05-2200252"]}
[2025-08-01 00:40:32] [DEBUG] 最终执行的SQL | {"finalQuery":"SELECT\n          xpo.BillNo as id,\n          xpo.BillNo as task_number,\n          xpo.ProjectId as project_id,\n          xpo.BizPartnerId as construction_unit_id,\n          CONCAT(\n            ISNULL(xpo.MaterialId, ''),\n            CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_ImperviousId, ''),\n            CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n            ISNULL(xpo.X_FolderId, '')\n          ) as strength_grade,\n          xpo.X_JZPart as part_name,\n          CASE\n            WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n            THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n            ELSE NULL\n          END as scheduled_time,\n          ISNULL(xpo.X_SupplyState, 0) as supply_status,\n          NULL as actual_time,\n          NULL as feedback_user,\n          1 as status,\n          cp.ProjectName as project_name,\n          ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n          cp.ProjectId as project_code\n         FROM dbo.X_ppProduceOrder xpo\n         LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n         LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n         WHERE xpo.BillNo = @param0"}
[2025-08-01 00:40:32] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:32] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = ? AND cp.X_OrgId = ?\n      ","params":["B05AJA220003","1007"]}
[2025-08-01 00:40:32] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT cp.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.comProject cp\n      WHERE cp.ProjectId = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:40:32] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:32] [DEBUG] 执行SQL查询 | {"query":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      ","params":[]}
[2025-08-01 00:40:32] [DEBUG] 最终执行的SQL | {"finalQuery":"\n        SELECT COUNT(*) as table_exists\n        FROM INFORMATION_SCHEMA.TABLES\n        WHERE TABLE_NAME = 'feedbacks' AND TABLE_CATALOG = 'NBSTEST'\n      "}
[2025-08-01 00:40:32] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"table_exists":0}]],"recordset":[{"table_exists":0}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:32] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:40:32] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008832614\u0000\u0000"}
[2025-08-01 00:40:32] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:40:32] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:40:32] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:40:32] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:40:32] [DEBUG] 根据ID查找工程 | {"id":"B05AJA220003"}
[2025-08-01 00:40:32] [DEBUG] 执行SQL查询 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = ?\n        ","params":["B05AJA220003"]}
[2025-08-01 00:40:32] [DEBUG] 最终执行的SQL | {"finalQuery":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.ProjectId = @param0\n        "}
[2025-08-01 00:40:32] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}]],"recordset":[{"id":"B05AJA220003","name":"厂区给水系统改造","code":"B05AJA220003","construction_unit_id":"K150330","construction_unit":"厦门翔义混凝土有限公司","company_id":"1007","implementation_status":1}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:40:32] [DEBUG] 从原始表找到工程 | {"projectId":"B05AJA220003","projectName":"厂区给水系统改造","constructionUnit":"厦门翔义混凝土有限公司"}
[2025-08-01 00:40:32] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = ?\n     ORDER BY xpo.DemandBeginDate DESC","params":["B05AJA220003"]}
[2025-08-01 00:40:32] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT\n        xpo.BillNo as id,\n        xpo.BillNo as task_number,\n        xpo.ProjectId as project_id,\n        xpo.BizPartnerId as construction_unit_id,\n        CONCAT(\n          ISNULL(xpo.MaterialId, ''),\n          CASE WHEN xpo.MaterialId IS NOT NULL AND xpo.X_ImperviousId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_ImperviousId, ''),\n          CASE WHEN (xpo.MaterialId IS NOT NULL OR xpo.X_ImperviousId IS NOT NULL) AND xpo.X_FolderId IS NOT NULL THEN ' ' ELSE '' END,\n          ISNULL(xpo.X_FolderId, '')\n        ) as strength_grade,\n        xpo.X_JZPart as part_name,\n        CASE\n          WHEN xpo.DemandBeginDate IS NOT NULL AND xpo.DemandBeginDate > 0\n          THEN CONVERT(DATETIME, CAST(xpo.DemandBeginDate AS VARCHAR(8)), 112)\n          ELSE NULL\n        END as scheduled_time,\n        ISNULL(xpo.X_SupplyState, 0) as supply_status,\n        NULL as actual_time,\n        NULL as feedback_user,\n        1 as status,\n        cp.ProjectName as project_name,\n        ISNULL(cbp.BizPartnerName, 'Unknown') as construction_unit,\n        cp.ProjectId as project_code,\n        ISNULL(feedback_stats.feedback_count, 0) as feedback_count\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n      LEFT JOIN (\n        SELECT\n          f.TaskNumber,\n          COUNT(DISTINCT f.Id) as feedback_count\n        FROM dbo.CU_feedbacks f\n        WHERE f.Status = 1\n        GROUP BY f.TaskNumber\n      ) feedback_stats ON xpo.BillNo = feedback_stats.TaskNumber\n      WHERE xpo.ProjectId = @param0\n     ORDER BY xpo.DemandBeginDate DESC"}
[2025-08-01 00:40:32] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":12}]],"recordset":[{"id":"B05-2200252","task_number":"B05-2200252","project_id":"B05AJA220003","construction_unit_id":"K150330","strength_grade":"A01-C25-01  ","part_name":"给水系统改造硬化","scheduled_time":"2022-01-09T00:00:00.000Z","supply_status":0,"actual_time":null,"feedback_user":null,"status":1,"project_name":"厂区给水系统改造","construction_unit":"厦门翔义混凝土有限公司","project_code":"B05AJA220003","feedback_count":12}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:41:03] [DEBUG] 认证检查 | {"hasToken":true,"tokenLength":44}
[2025-08-01 00:41:03] [DEBUG] Token解码结果 | {"decoded":"2023043:黄煜斌:1754008863615\u0000\u0000"}
[2025-08-01 00:41:03] [DEBUG] Token解析结果 | {"userId":"2023043","username":"黄煜斌","userIdType":"string"}
[2025-08-01 00:41:03] [DEBUG] 用户ID解析 | {"originalUserId":"2023043","userIdType":"string"}
[2025-08-01 00:41:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-01 00:41:03] [DEBUG] 用户通过token认证 | {"userId":2023043}
[2025-08-01 00:41:03] [DEBUG] 执行SQL查询 | {"query":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = ? AND cp.X_OrgId = ?\n      ","params":["B05-2200252","1007"]}
[2025-08-01 00:41:03] [DEBUG] 最终执行的SQL | {"finalQuery":"\n      SELECT xpo.BillNo, xpo.ProjectId, cp.ProjectName, cp.X_OrgId\n      FROM dbo.X_ppProduceOrder xpo\n      LEFT JOIN dbo.comProject cp ON xpo.ProjectId = cp.ProjectId\n      WHERE xpo.BillNo = @param0 AND cp.X_OrgId = @param1\n      "}
[2025-08-01 00:41:03] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}]],"recordset":[{"BillNo":"B05-2200252","ProjectId":"B05AJA220003","ProjectName":"厂区给水系统改造","X_OrgId":"1007"}],"output":{},"rowsAffected":[1]}}
[2025-08-01 00:41:03] [DEBUG] 执行SQL查询 | {"query":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)","params":["B05-2200252","2023043","2025-08-01 08:40:00","","反馈类别3",118.14577,24.5118,"福建省厦门市湖里区禾山街道金湖三里30-46禹洲·香槟城 (禹洲·香槟城北门) (24.511800, 118.145770)","authorized"]}
[2025-08-01 00:41:03] [DEBUG] 最终执行的SQL | {"finalQuery":"INSERT INTO dbo.CU_feedbacks\n                   (TaskNumber, FeedbackUserId, FeedbackTime, Notes, Category, Longitude, Latitude, LocationDesc, LocationStatus)\n                   VALUES (@param0, @param1, @param2, @param3, @param4, @param5, @param6, @param7, @param8); SELECT SCOPE_IDENTITY() as insertId;"}
[2025-08-01 00:41:03] [DEBUG] SQL执行结果 | {"result":{"recordsets":[[{"insertId":5014}]],"recordset":[{"insertId":5014}],"output":{},"rowsAffected":[1,1]}}
